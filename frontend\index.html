<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evidence Protection System</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-shield-alt"></i>
                <span>Evidence Protection System</span>
            </div>
            <div class="nav-menu" id="nav-menu">
                <!-- Public Navigation (shown when not logged in) -->
                <div class="nav-item" id="nav-home" style="display: none;">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </div>
                <div class="nav-item" id="nav-about" style="display: none;">
                    <i class="fas fa-info-circle"></i>
                    <span>About Us</span>
                </div>
                <div class="nav-item" id="nav-login" style="display: none;">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Login</span>
                </div>

                <!-- Authenticated Navigation (shown when logged in) -->
                <div class="nav-item" id="nav-dashboard" style="display: none;">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </div>
                <div class="nav-item" id="nav-upload" style="display: none;">
                    <i class="fas fa-upload"></i>
                    <span>Upload Evidence</span>
                </div>
                <div class="nav-item" id="nav-evidence" style="display: none;">
                    <i class="fas fa-folder-open"></i>
                    <span>View Evidence</span>
                </div>
                <div class="nav-item" id="nav-verify" style="display: none;">
                    <i class="fas fa-check-circle"></i>
                    <span>Verify Evidence</span>
                </div>
                <div class="nav-item" id="nav-admin" style="display: none;">
                    <i class="fas fa-users-cog"></i>
                    <span>Admin Panel</span>
                </div>
                <div class="nav-item" id="nav-profile" style="display: none;">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </div>
                <div class="nav-item" id="nav-logout" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </div>
            </div>
            <div class="user-info" id="user-info">
                <span id="user-name">User</span>
                <span id="user-role" class="role-badge">Role</span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Login Page -->
        <div class="page" id="login-page">
            <div class="login-container">
                <div class="login-header">
                    <div class="login-logo">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h1>Evidence Protection System</h1>
                    <p class="login-subtitle">Secure blockchain-based evidence management for law enforcement and legal professionals</p>
                </div>

                <div class="login-description">
                    <div class="feature-grid">
                        <div class="feature-item">
                            <i class="fas fa-lock"></i>
                            <h3>Secure Storage</h3>
                            <p>Military-grade encryption protects all evidence files</p>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-link"></i>
                            <h3>Blockchain Verified</h3>
                            <p>Immutable blockchain records ensure evidence integrity</p>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <h3>Role-Based Access</h3>
                            <p>Controlled access for police, lawyers, and administrators</p>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-history"></i>
                            <h3>Audit Trail</h3>
                            <p>Complete tracking of all evidence handling activities</p>
                        </div>
                    </div>
                </div>

                <div class="login-form-container">
                    <h2>Access Your Account</h2>
                    <form class="login-form" id="login-form">
                        <div class="form-group">
                            <label for="username">Username or Email</label>
                            <input type="text" id="username" name="username" required placeholder="Enter your username">
                        </div>
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" id="password" name="password" required placeholder="Enter your password">
                        </div>
                        <button type="submit" class="btn btn-primary" id="login-submit-btn">
                            <i class="fas fa-sign-in-alt"></i>
                            Secure Login
                        </button>
                    </form>




                </div>

                <div class="login-footer">
                    <div class="security-notice">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p><strong>Authorized Personnel Only</strong> - This system is for official use by law enforcement and legal professionals. Unauthorized access is prohibited and monitored.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Home Page -->
        <div class="page hidden" id="home-page">
            <div class="home-container">
                <div class="hero-section">
                    <div class="hero-content">
                        <h1>Evidence Protection System</h1>
                        <p class="hero-subtitle">Next-generation blockchain-based evidence management for law enforcement and legal professionals</p>
                        <div class="hero-buttons">
                            <button class="btn btn-primary" onclick="window.app.showPage('login')">
                                <i class="fas fa-sign-in-alt"></i>
                                Access System
                            </button>
                            <button class="btn btn-secondary" onclick="window.app.showPage('about')">
                                <i class="fas fa-info-circle"></i>
                                About Us
                            </button>
                        </div>
                    </div>
                    <div class="hero-image">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                </div>

                <div class="features-section">
                    <h2>Why Choose Our Evidence Protection System?</h2>
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <h3>Military-Grade Security</h3>
                            <p>Advanced encryption and security protocols protect sensitive evidence data from unauthorized access and tampering.</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-link"></i>
                            </div>
                            <h3>Blockchain Integrity</h3>
                            <p>Immutable blockchain technology ensures evidence integrity and provides cryptographic proof of authenticity.</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3>Role-Based Access</h3>
                            <p>Granular permission system ensures only authorized personnel can access specific evidence based on their role.</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <h3>Complete Audit Trail</h3>
                            <p>Every action is logged and tracked, providing a complete chain of custody for legal proceedings.</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-cloud"></i>
                            </div>
                            <h3>Cloud-Based Storage</h3>
                            <p>Secure cloud infrastructure ensures evidence is always accessible while maintaining the highest security standards.</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <h3>Mobile Responsive</h3>
                            <p>Access the system from any device with our responsive design that works on desktop, tablet, and mobile.</p>
                        </div>
                    </div>
                </div>

                <div class="stats-section">
                    <h2>Trusted by Law Enforcement</h2>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">99.9%</div>
                            <div class="stat-label">Uptime Guarantee</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">256-bit</div>
                            <div class="stat-label">Encryption Standard</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">24/7</div>
                            <div class="stat-label">Security Monitoring</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">Blockchain Verified</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- About Us Page -->
        <div class="page hidden" id="about-page">
            <div class="about-container">
                <div class="about-header">
                    <h1>About Evidence Protection System</h1>
                    <p>Leading the future of secure, blockchain-based evidence management for law enforcement and legal professionals worldwide</p>
                </div>

                <div class="about-content">
                    <div class="mission-section">
                        <div class="mission-card">
                            <div class="mission-icon">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <h2>Our Mission</h2>
                            <p>To revolutionize evidence management through cutting-edge blockchain technology, ensuring the highest levels of security, integrity, and accessibility for law enforcement and legal professionals worldwide.</p>
                        </div>

                        <div class="mission-card">
                            <div class="mission-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <h2>Our Vision</h2>
                            <p>A world where digital evidence is completely secure, tamper-proof, and instantly verifiable, enabling justice systems to operate with unprecedented transparency and trust.</p>
                        </div>

                        <div class="mission-card">
                            <div class="mission-icon">
                                <i class="fas fa-heart"></i>
                            </div>
                            <h2>Our Values</h2>
                            <p>Security, integrity, innovation, and justice. We believe that technology should serve justice, protect the innocent, and ensure that truth prevails in every legal proceeding.</p>
                        </div>
                    </div>

                    <div class="story-section">
                        <h2>Our Story</h2>
                        <div class="story-content">
                            <div class="story-text">
                                <p>Founded in 2024, Evidence Protection System emerged from a critical need in the justice system: ensuring the absolute integrity and security of digital evidence. Our team of cybersecurity experts, blockchain developers, and legal professionals came together with a shared vision.</p>

                                <p>We recognized that traditional evidence management systems were vulnerable to tampering, loss, and unauthorized access. With the increasing digitization of evidence and the growing sophistication of cyber threats, law enforcement and legal professionals needed a revolutionary solution.</p>

                                <p>Our breakthrough came with the integration of blockchain technology, creating an immutable ledger for evidence tracking. Combined with military-grade encryption and role-based access controls, we've created the most secure evidence management platform available today.</p>

                                <p>Today, we're proud to serve law enforcement agencies, legal firms, and judicial institutions worldwide, protecting the integrity of justice through technology.</p>
                            </div>
                            <div class="story-stats">
                                <div class="stat-highlight">
                                    <div class="stat-number">100%</div>
                                    <div class="stat-text">Evidence Integrity Guaranteed</div>
                                </div>
                                <div class="stat-highlight">
                                    <div class="stat-number">24/7</div>
                                    <div class="stat-text">Security Monitoring</div>
                                </div>
                                <div class="stat-highlight">
                                    <div class="stat-number">256-bit</div>
                                    <div class="stat-text">Military-Grade Encryption</div>
                                </div>
                                <div class="stat-highlight">
                                    <div class="stat-number">99.9%</div>
                                    <div class="stat-text">System Uptime</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="team-section">
                        <h2>Why Choose Us?</h2>
                        <div class="reasons-grid">
                            <div class="reason-card">
                                <i class="fas fa-award"></i>
                                <h3>Industry Leading</h3>
                                <p>Recognized as the most advanced evidence protection platform by cybersecurity experts and legal professionals.</p>
                            </div>
                            <div class="reason-card">
                                <i class="fas fa-users-cog"></i>
                                <h3>Expert Team</h3>
                                <p>Our team combines decades of experience in cybersecurity, blockchain technology, and legal systems.</p>
                            </div>
                            <div class="reason-card">
                                <i class="fas fa-certificate"></i>
                                <h3>Certified Secure</h3>
                                <p>SOC 2 compliant, ISO 27001 certified, and GDPR ready. We meet the highest international security standards.</p>
                            </div>
                            <div class="reason-card">
                                <i class="fas fa-handshake"></i>
                                <h3>Trusted Partner</h3>
                                <p>Trusted by law enforcement agencies and legal firms worldwide for their most sensitive evidence.</p>
                            </div>
                        </div>
                    </div>

                    <div class="contact-cta">
                        <h2>Ready to Secure Your Evidence?</h2>
                        <p>Join thousands of law enforcement professionals who trust our platform with their most critical evidence.</p>
                        <div class="cta-buttons">
                            <button class="btn btn-primary" onclick="window.app.showPage('login')">
                                <i class="fas fa-sign-in-alt"></i>
                                Access System
                            </button>
                            <div class="contact-info-inline">
                                <p><i class="fas fa-phone"></i> +1 (555) 123-EVIDENCE</p>
                                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Page -->
        <div class="page hidden" id="dashboard-page">
            <div class="page-header">
                <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
                <p>Evidence Protection System Overview</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="total-evidence">0</h3>
                        <p>Total Evidence</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="verified-evidence">0</h3>
                        <p>Verified Evidence</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="recent-uploads">0</h3>
                        <p>Recent Uploads</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="blockchain-status">Checking...</h3>
                        <p>Blockchain Status</p>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="recent-activity">
                    <h2><i class="fas fa-history"></i> Recent Activity</h2>
                    <div class="activity-list" id="activity-list">
                        <!-- Activity items will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Evidence Page -->
        <div class="page hidden" id="upload-page">
            <div class="page-header">
                <h1><i class="fas fa-upload"></i> Upload Evidence</h1>
                <p>Securely upload digital evidence to the blockchain</p>
            </div>
            
            <form class="upload-form" id="upload-form" enctype="multipart/form-data">
                <div class="form-row">
                    <div class="form-group">
                        <label for="case-id">Case ID *</label>
                        <input type="text" id="case-id" name="caseId" required>
                    </div>
                    <div class="form-group">
                        <label for="evidence-file">Evidence File *</label>
                        <input type="file" id="evidence-file" name="evidenceFile" required>
                        <small>Max file size: 50MB. Supported formats: Images, Videos, Audio, Documents</small>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="description">Description *</label>
                    <textarea id="description" name="description" rows="4" required 
                              placeholder="Provide a detailed description of the evidence..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="tags">Tags (optional)</label>
                    <input type="text" id="tags" name="tags" 
                           placeholder="Enter tags separated by commas (e.g., weapon, fingerprint, DNA)">
                </div>
                
                <div class="file-preview" id="file-preview" style="display: none;">
                    <h3>File Preview</h3>
                    <div class="preview-content" id="preview-content"></div>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-upload"></i>
                    Upload Evidence
                </button>
            </form>
        </div>

        <!-- View Evidence Page -->
        <div class="page hidden" id="evidence-page">
            <div class="page-header">
                <h1><i class="fas fa-folder-open"></i> Evidence Repository</h1>
                <p>View and manage uploaded evidence</p>
            </div>
            
            <div class="evidence-filters">
                <div class="filter-group">
                    <input type="text" id="search-case" placeholder="Search by Case ID">
                    <select id="filter-verified">
                        <option value="">All Evidence</option>
                        <option value="true">Verified Only</option>
                        <option value="false">Unverified Only</option>
                    </select>
                    <button class="btn btn-secondary" id="apply-filters">
                        <i class="fas fa-filter"></i>
                        Apply Filters
                    </button>
                </div>
            </div>
            
            <div class="evidence-grid" id="evidence-grid">
                <!-- Evidence cards will be populated here -->
            </div>
            
            <div class="pagination" id="pagination">
                <!-- Pagination controls will be populated here -->
            </div>
        </div>

        <!-- Verify Evidence Page -->
        <div class="page hidden" id="verify-page">
            <div class="page-header">
                <h1><i class="fas fa-check-circle"></i> Verify Evidence</h1>
                <p>Verify evidence integrity using blockchain technology</p>
            </div>

            <div class="verify-form">
                <div class="form-group">
                    <label for="verify-evidence-id">Evidence ID</label>
                    <input type="text" id="verify-evidence-id" placeholder="Enter evidence ID to verify">
                </div>
                <button class="btn btn-primary" id="verify-btn">
                    <i class="fas fa-search"></i>
                    Verify Evidence
                </button>
            </div>

            <div class="verification-result" id="verification-result" style="display: none;">
                <!-- Verification results will be displayed here -->
            </div>

            <!-- Unverified Evidence List -->
            <div class="unverified-evidence-section" style="margin-top: 2rem;">
                <div class="section-header">
                    <h2><i class="fas fa-exclamation-triangle"></i> Unverified Evidence</h2>
                    <p>Click on any evidence item to verify it</p>
                </div>

                <div class="loading-spinner" id="unverified-loading" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> Loading unverified evidence...
                </div>

                <div class="unverified-evidence-list" id="unverified-evidence-list">
                    <!-- Unverified evidence items will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Admin Panel Page -->
        <div class="page hidden" id="admin-page">
            <div class="admin-layout">
                <!-- Admin Side Navigation -->
                <div class="admin-sidebar">
                    <div class="admin-sidebar-header">
                        <h2><i class="fas fa-users-cog"></i> Admin Panel</h2>
                        <p>System Management</p>
                    </div>

                    <nav class="admin-nav">
                        <a href="#" class="admin-nav-item active" data-tab="users">
                            <i class="fas fa-users"></i>
                            <span>User Management</span>
                        </a>
                        <a href="#" class="admin-nav-item" data-tab="stats">
                            <i class="fas fa-chart-bar"></i>
                            <span>System Statistics</span>
                        </a>
                        <a href="#" class="admin-nav-item" data-tab="blockchain">
                            <i class="fas fa-link"></i>
                            <span>Blockchain Health</span>
                        </a>
                        <a href="#" class="admin-nav-item" data-tab="logs">
                            <i class="fas fa-list-alt"></i>
                            <span>Real-time Logs</span>
                        </a>
                        <a href="#" class="admin-nav-item" data-tab="export">
                            <i class="fas fa-download"></i>
                            <span>Legal Export</span>
                        </a>
                        <a href="#" class="admin-nav-item" data-tab="settings">
                            <i class="fas fa-cog"></i>
                            <span>System Settings</span>
                        </a>
                    </nav>
                </div>

                <!-- Admin Main Content -->
                <div class="admin-main-content">
                    <!-- Mobile Header -->
                    <div class="admin-mobile-header" style="display: none;">
                        <h3>Admin Panel</h3>
                        <button class="admin-mobile-toggle" id="admin-mobile-toggle">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>

                <!-- User Management Tab -->
                <div class="tab-content active" id="users-tab">
                    <div class="admin-content-header">
                        <h3><i class="fas fa-users"></i> User Management</h3>
                        <p>Create, edit, and manage user accounts and permissions</p>
                    </div>

                    <div class="users-header">
                        <div class="users-controls">
                            <div class="search-box">
                                <input type="text" id="user-search" placeholder="Search users by name, email, or department...">
                                <i class="fas fa-search"></i>
                            </div>
                            <select id="role-filter">
                                <option value="">All Roles</option>
                                <option value="admin">Administrator</option>
                                <option value="police">Police Officer</option>
                                <option value="lawyer">Lawyer</option>
                            </select>
                            <select id="status-filter">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                            <button class="btn btn-primary" id="add-user-btn">
                                <i class="fas fa-plus"></i> Add New User
                            </button>
                        </div>
                    </div>

                    <div class="users-stats">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h4 id="total-users-count">0</h4>
                                <p>Total Users</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stat-info">
                                <h4 id="active-users-count">0</h4>
                                <p>Active Users</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div class="stat-info">
                                <h4 id="admin-users-count">0</h4>
                                <p>Administrators</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="stat-info">
                                <h4 id="new-users-count">0</h4>
                                <p>New This Month</p>
                            </div>
                        </div>
                    </div>

                    <div class="users-table-container">
                        <table class="users-table">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="select-all-users">
                                    </th>
                                    <th>User Information</th>
                                    <th>Role</th>
                                    <th>Department</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="users-table-body">
                                <!-- Users will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <div class="users-table-footer">
                        <div class="bulk-actions">
                            <select id="bulk-action-select" disabled>
                                <option value="">Bulk Actions</option>
                                <option value="activate">Activate Selected</option>
                                <option value="deactivate">Deactivate Selected</option>
                                <option value="delete">Delete Selected</option>
                            </select>
                            <button class="btn btn-secondary" id="apply-bulk-action" disabled>
                                Apply
                            </button>
                        </div>
                        <div class="pagination" id="users-pagination">
                            <!-- Pagination will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- System Statistics Tab -->
                <div class="tab-content" id="stats-tab">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-users">0</h3>
                                <p>Total Users</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-folder"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-evidence">0</h3>
                                <p>Total Evidence</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="verified-evidence">0</h3>
                                <p>Verified Evidence</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="verification-rate">0%</h3>
                                <p>Verification Rate</p>
                            </div>
                        </div>
                    </div>

                    <div class="charts-section">
                        <div class="chart-container">
                            <h3>User Distribution by Role</h3>
                            <div id="role-chart"></div>
                        </div>
                        <div class="chart-container">
                            <h3>Recent Activity</h3>
                            <div id="activity-list"></div>
                        </div>
                    </div>
                </div>

                <!-- Blockchain Health Tab -->
                <div class="tab-content" id="blockchain-tab">
                    <div class="blockchain-health-section">
                        <h3><i class="fas fa-link"></i> Blockchain Health Monitor</h3>
                        <div class="health-refresh">
                            <button class="btn btn-secondary" id="refresh-blockchain-health">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                        <div class="blockchain-status" id="blockchain-status">
                            <!-- Blockchain health data will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Real-time Logs Tab -->
                <div class="tab-content" id="logs-tab">
                    <div class="logs-section">
                        <h3><i class="fas fa-list-alt"></i> Real-time System Logs</h3>
                        <div class="logs-controls">
                            <button class="btn btn-secondary" id="refresh-logs">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button class="btn btn-secondary" id="auto-refresh-toggle">
                                <i class="fas fa-play"></i> Auto Refresh
                            </button>
                        </div>
                        <div class="logs-container">
                            <div class="logs-section-header">
                                <h4>Recent Access Logs</h4>
                            </div>
                            <div class="logs-list" id="access-logs">
                                <!-- Access logs will be loaded here -->
                            </div>
                            <div class="logs-section-header">
                                <h4>Recent Verification Logs</h4>
                            </div>
                            <div class="logs-list" id="verification-logs">
                                <!-- Verification logs will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Legal Export Tab -->
                <div class="tab-content" id="export-tab">
                    <div class="export-section">
                        <h3><i class="fas fa-download"></i> Legal Export with Blockchain Proof</h3>
                        <p>Generate comprehensive case bundles with files, metadata, blockchain hashes, timestamps, and chain of custody logs.</p>

                        <div class="export-form">
                            <div class="form-group">
                                <label for="export-case-id">Case ID</label>
                                <input type="text" id="export-case-id" placeholder="Enter case ID to export">
                            </div>
                            <button class="btn btn-primary" id="export-case-btn">
                                <i class="fas fa-download"></i> Generate Legal Export
                            </button>
                        </div>

                        <div class="export-info">
                            <h4>Export Contents:</h4>
                            <ul>
                                <li><i class="fas fa-file"></i> Original evidence files</li>
                                <li><i class="fas fa-database"></i> Complete metadata (JSON format)</li>
                                <li><i class="fas fa-link"></i> Blockchain transaction hashes</li>
                                <li><i class="fas fa-clock"></i> Timestamps and access logs</li>
                                <li><i class="fas fa-list"></i> Chain of custody reports</li>
                                <li><i class="fas fa-file-alt"></i> Case summary report</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- System Settings Tab -->
                <div class="tab-content" id="settings-tab">
                    <div class="settings-section">
                        <h3>System Configuration</h3>
                        <div class="setting-item">
                            <label>Maximum File Size (MB)</label>
                            <input type="number" id="max-file-size" value="100">
                        </div>
                        <div class="setting-item">
                            <label>Session Timeout (minutes)</label>
                            <input type="number" id="session-timeout" value="60">
                        </div>
                        <div class="setting-item">
                            <label>Enable Two-Factor Authentication</label>
                            <input type="checkbox" id="enable-2fa">
                        </div>
                        <button class="btn btn-primary">Save Settings</button>
                    </div>

                    <div class="settings-section" style="margin-top: 2rem;">
                        <h3>Testing & Development</h3>
                        <p>Tools for testing the system functionality</p>
                        <button class="btn btn-secondary" id="create-sample-evidence-btn">
                            <i class="fas fa-plus"></i> Create Sample Evidence
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Page -->
        <div class="page hidden" id="profile-page">
            <div class="page-header">
                <h1><i class="fas fa-user"></i> User Profile</h1>
                <p>Manage your account settings</p>
            </div>
            
            <div class="profile-content">
                <div class="profile-info">
                    <h2>Profile Information</h2>
                    <div class="info-grid" id="profile-info">
                        <!-- Profile information will be populated here -->
                    </div>
                </div>
                
                <div class="profile-actions">
                    <button class="btn btn-secondary" id="change-password-btn">
                        <i class="fas fa-key"></i>
                        Change Password
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" id="footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-shield-alt"></i>
                        <span>Evidence Protection System</span>
                    </div>
                    <p class="footer-description">
                        Secure, blockchain-based evidence management system designed for law enforcement agencies,
                        legal professionals, and judicial institutions worldwide.
                    </p>
                    <div class="footer-social">
                        <a href="#" class="social-link" title="LinkedIn">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="social-link" title="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link" title="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>System Features</h3>
                    <ul class="footer-links">
                        <li><a href="#"><i class="fas fa-upload"></i> Evidence Upload</a></li>
                        <li><a href="#"><i class="fas fa-search"></i> Evidence Search</a></li>
                        <li><a href="#"><i class="fas fa-check-circle"></i> Integrity Verification</a></li>
                        <li><a href="#"><i class="fas fa-download"></i> Secure Download</a></li>
                        <li><a href="#"><i class="fas fa-history"></i> Audit Trails</a></li>
                        <li><a href="#"><i class="fas fa-users"></i> User Management</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Security & Compliance</h3>
                    <ul class="footer-links">
                        <li><a href="#"><i class="fas fa-certificate"></i> Security Certifications</a></li>
                        <li><a href="#"><i class="fas fa-gavel"></i> Legal Compliance</a></li>
                        <li><a href="#"><i class="fas fa-lock"></i> Privacy Policy</a></li>
                        <li><a href="#"><i class="fas fa-file-contract"></i> Terms of Service</a></li>
                        <li><a href="#"><i class="fas fa-shield-alt"></i> Security Whitepaper</a></li>
                        <li><a href="#"><i class="fas fa-bug"></i> Report Vulnerability</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Support & Resources</h3>
                    <ul class="footer-links">
                        <li><a href="#"><i class="fas fa-book"></i> User Documentation</a></li>
                        <li><a href="#"><i class="fas fa-question-circle"></i> FAQ</a></li>
                        <li><a href="#"><i class="fas fa-headset"></i> Technical Support</a></li>
                        <li><a href="#"><i class="fas fa-graduation-cap"></i> Training Materials</a></li>
                        <li><a href="#"><i class="fas fa-code"></i> API Documentation</a></li>
                        <li><a href="#"><i class="fas fa-comments"></i> Community Forum</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <div>
                                <strong>Headquarters</strong><br>
                                123 Security Boulevard<br>
                                Tech City, TC 12345
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <div>
                                <strong>24/7 Support</strong><br>
                                +1 (555) 123-EVIDENCE<br>
                                +1 (555) 123-SUPPORT
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <div>
                                <strong>Email</strong><br>
                                <EMAIL><br>
                                <EMAIL>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <div class="footer-credits">
                        <p>&copy; 2024 Evidence Protection System. All rights reserved.</p>
                        <p>Developed with <i class="fas fa-heart" style="color: #e74c3c;"></i> for law enforcement and legal professionals worldwide.</p>
                    </div>

                    <div class="footer-tech">
                        <div class="tech-stack">
                            <span class="tech-label">Powered by:</span>
                            <div class="tech-items">
                                <span class="tech-item">
                                    <i class="fab fa-node-js"></i> Node.js
                                </span>
                                <span class="tech-item">
                                    <i class="fas fa-database"></i> MongoDB
                                </span>
                                <span class="tech-item">
                                    <i class="fab fa-ethereum"></i> Ethereum
                                </span>
                                <span class="tech-item">
                                    <i class="fas fa-link"></i> Blockchain
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="footer-legal">
                        <div class="legal-links">
                            <a href="#">Privacy Policy</a>
                            <span>|</span>
                            <a href="#">Terms of Service</a>
                            <span>|</span>
                            <a href="#">Cookie Policy</a>
                            <span>|</span>
                            <a href="#">Accessibility</a>
                        </div>
                        <div class="compliance-badges">
                            <span class="badge">SOC 2 Compliant</span>
                            <span class="badge">ISO 27001</span>
                            <span class="badge">GDPR Ready</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

                </div> <!-- End admin-main-content -->
            </div> <!-- End admin-layout -->

            <!-- Mobile Overlay -->
            <div class="admin-overlay" id="admin-overlay"></div>
        </div> <!-- End admin-page -->

    </footer>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Processing...</p>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal hidden" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Modal Title</h2>
                <button class="modal-close" id="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Modal content will be populated here -->
            </div>
        </div>
    </div>

    <!-- User Management Modal -->
    <div class="modal hidden" id="user-modal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h2 id="user-modal-title">Add New User</h2>
                <button class="modal-close" onclick="closeUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="user-form">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="user-username">Username *</label>
                        <input type="text" id="user-username" required placeholder="Enter unique username">
                        <small class="form-help">Must be unique and at least 3 characters</small>
                    </div>
                    <div class="form-group">
                        <label for="user-email">Email Address *</label>
                        <input type="email" id="user-email" required placeholder="<EMAIL>">
                        <small class="form-help">Must be a valid email address</small>
                    </div>
                    <div class="form-group">
                        <label for="user-fullname">Full Name *</label>
                        <input type="text" id="user-fullname" required placeholder="Enter full name">
                    </div>
                    <div class="form-group">
                        <label for="user-phone">Phone Number</label>
                        <input type="tel" id="user-phone" placeholder="+****************">
                    </div>
                    <div class="form-group">
                        <label for="user-role">Role *</label>
                        <select id="user-role" required>
                            <option value="">Select Role</option>
                            <option value="admin">Administrator</option>
                            <option value="police">Police Officer</option>
                            <option value="lawyer">Lawyer</option>
                        </select>
                        <small class="form-help">Determines user permissions and access level</small>
                    </div>
                    <div class="form-group">
                        <label for="user-department">Department</label>
                        <input type="text" id="user-department" placeholder="e.g., Criminal Investigation">
                    </div>
                    <div class="form-group">
                        <label for="user-badge-number">Badge/ID Number</label>
                        <input type="text" id="user-badge-number" placeholder="Enter badge or ID number">
                    </div>
                    <div class="form-group">
                        <label for="user-status">Account Status</label>
                        <select id="user-status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="form-group" id="password-group">
                        <label for="user-password">Password *</label>
                        <input type="password" id="user-password" minlength="6" placeholder="Enter secure password">
                        <small class="form-help">Minimum 6 characters, include letters and numbers</small>
                    </div>
                    <div class="form-group" id="confirm-password-group">
                        <label for="user-confirm-password">Confirm Password *</label>
                        <input type="password" id="user-confirm-password" minlength="6" placeholder="Confirm password">
                    </div>
                    <div class="form-group full-width">
                        <label for="user-notes">Notes</label>
                        <textarea id="user-notes" rows="3" placeholder="Additional notes about this user (optional)"></textarea>
                    </div>
                </div>

                <div class="form-section" id="edit-only-section" style="display: none;">
                    <h4>Account Management</h4>
                    <div class="form-actions-grid">
                        <button type="button" class="btn btn-warning" id="reset-password-btn">
                            <i class="fas fa-key"></i> Reset Password
                        </button>
                        <button type="button" class="btn btn-info" id="send-welcome-email-btn">
                            <i class="fas fa-envelope"></i> Send Welcome Email
                        </button>
                        <button type="button" class="btn btn-secondary" id="view-login-history-btn">
                            <i class="fas fa-history"></i> Login History
                        </button>
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeUserModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <span id="user-submit-text">Create User</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal hidden" id="confirmation-modal">
        <div class="modal-content small-modal">
            <div class="modal-header">
                <h3 id="confirmation-title">Confirm Action</h3>
                <button class="modal-close" onclick="closeConfirmationModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="confirmation-message">Are you sure you want to perform this action?</p>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeConfirmationModal()">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirm-action-btn">Confirm</button>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toast-container">
        <!-- Toast notifications will be added here -->
    </div>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/evidence.js"></script>
    <script src="js/verify.js"></script>
    <script src="js/profile.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/app.js"></script>

    <!-- Initialize Application -->
    <script>
        // Initialize global instances
        window.api = new API();
        // AuthManager is already initialized in auth.js
        window.adminManager = new AdminManager();
        window.app = new App();
    </script>
</body>
</html>
