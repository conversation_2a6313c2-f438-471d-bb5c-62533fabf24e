// Main Application Controller
class App {
    constructor() {
        this.currentPage = 'login';
        this.preventRedirect = false;
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.setupNavigation();
        this.setupModalHandlers();
        this.setupGlobalEventListeners();
        
        // Initialize the application
        this.initializeApp();
    }

    setupNavigation() {
        // Navigation item click handlers
        const navItems = {
            'nav-home': () => this.showPage('home'),
            'nav-about': () => this.showPage('about'),
            'nav-login': () => this.showPage('login'),
            'nav-dashboard': () => this.showPage('dashboard'),
            'nav-upload': () => this.showPage('upload'),
            'nav-evidence': () => this.showPage('evidence'),
            'nav-verify': () => this.showPage('verify'),
            'nav-admin': () => this.showPage('admin'),
            'nav-profile': () => this.showPage('profile'),
            'nav-logout': () => window.authManager ? window.authManager.logout() : console.error('AuthManager not available')
        };

        Object.keys(navItems).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('click', navItems[id]);
            }
        });
    }

    setupModalHandlers() {
        // Modal close handlers
        const modalClose = document.getElementById('modal-close');
        const modal = document.getElementById('modal');

        if (modalClose) {
            modalClose.addEventListener('click', hideModal);
        }

        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    hideModal();
                }
            });
        }

        // Escape key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                hideModal();
            }
        });
    }

    setupGlobalEventListeners() {
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.page) {
                this.showPage(e.state.page, false);
            }
        });

        // Handle online/offline status
        window.addEventListener('online', () => {
            showToast('Connection restored', 'success');
        });

        window.addEventListener('offline', () => {
            showToast('Connection lost - working offline', 'warning');
        });

        // Handle unload to clean up
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        // Handle contact form submission
        document.addEventListener('submit', (event) => {
            if (event.target.id === 'contact-form') {
                event.preventDefault();
                this.handleContactForm(event);
            }
        });
    }

    handleContactForm(event) {
        const formData = new FormData(event.target);
        const contactData = {
            name: formData.get('name'),
            email: formData.get('email'),
            organization: formData.get('organization'),
            subject: formData.get('subject'),
            message: formData.get('message')
        };

        // Simulate form submission
        showSuccessNotification(
            'Message Sent Successfully!',
            `Thank you ${contactData.name}, we'll get back to you within 24 hours.`
        );

        // Reset form
        event.target.reset();
    }

    showPublicNavigation() {
        // Show navbar
        const navbar = document.getElementById('navbar');
        navbar.style.display = 'block';

        // Show public navigation items
        document.getElementById('nav-home').style.display = 'flex';
        document.getElementById('nav-about').style.display = 'flex';
        document.getElementById('nav-login').style.display = 'flex';

        // Hide authenticated navigation items
        document.getElementById('nav-dashboard').style.display = 'none';
        document.getElementById('nav-upload').style.display = 'none';
        document.getElementById('nav-evidence').style.display = 'none';
        document.getElementById('nav-verify').style.display = 'none';
        document.getElementById('nav-admin').style.display = 'none';
        document.getElementById('nav-profile').style.display = 'none';
        document.getElementById('nav-logout').style.display = 'none';

        // Hide user info
        document.getElementById('user-info').style.display = 'none';
    }

    initializeApp() {
        // Check if we should prevent redirect (during login process)
        if (this.preventRedirect) {
            console.log('App: Redirect prevented during login process');
            return;
        }

        // Check if user is authenticated
        if (isAuthenticated()) {
            // User is logged in, let auth manager handle the dashboard
            console.log('App: User is authenticated, letting auth manager handle dashboard');
            // Don't override auth manager's navigation - it will handle showing dashboard
            return;
        } else {
            // User is not logged in, show public navigation and login page
            console.log('App: User not authenticated, showing public navigation');
            this.showPublicNavigation();
            this.showPage('login');
        }
    }

    async initializeAuthenticatedApp() {
        try {
            // Ensure AuthManager is available
            if (!window.authManager) {
                console.error('AuthManager not available during app initialization');
                this.showPage('login');
                return;
            }

            // Load current user
            await window.authManager.loadCurrentUser();

            // Check for URL hash to determine initial page
            const hash = window.location.hash.substring(1); // Remove #
            const targetPage = hash || 'dashboard';

            // Show the target page (access control will be enforced)
            this.showPage(targetPage);
        } catch (error) {
            console.error('App initialization error:', error);
            // If there's an error, logout and show login
            if (window.authManager) {
                window.authManager.logout();
            } else {
                this.showPage('login');
            }
        }
    }

    showPage(pageName, updateHistory = true) {
        // Check if user can access this page
        if (!this.canAccessPage(pageName)) {
            console.warn(`Access denied to page: ${pageName}`);
            // Redirect to appropriate page based on login status
            if (!window.authManager || !window.authManager.isLoggedIn()) {
                this.showPage('login', false);
            } else {
                this.showPage('dashboard', false);
                if (typeof showErrorNotification === 'function') {
                    showErrorNotification('Access Denied', `You don't have permission to access the ${pageName} page.`);
                }
            }
            return;
        }

        // Cleanup previous page
        if (this.currentPage === 'admin' && window.adminManager) {
            window.adminManager.stopRealTimeSync();
        }

        // Hide all pages
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => page.classList.add('hidden'));

        // Remove active class from nav items
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => item.classList.remove('active'));

        // Show requested page
        const targetPage = document.getElementById(`${pageName}-page`);
        if (targetPage) {
            targetPage.classList.remove('hidden');
            this.currentPage = pageName;

            // Add active class to corresponding nav item
            const navItem = document.getElementById(`nav-${pageName}`);
            if (navItem) {
                navItem.classList.add('active');
            }

            // Update browser history
            if (updateHistory && pageName !== 'login') {
                history.pushState({ page: pageName }, '', `#${pageName}`);
            }

            // Load page-specific data
            this.loadPageData(pageName);
        }
    }

    async loadPageData(pageName) {
        try {
            switch (pageName) {
                case 'dashboard':
                    if (window.dashboardManager) {
                        await dashboardManager.loadDashboard();
                    }
                    break;

                case 'evidence':
                    if (window.evidenceManager) {
                        await evidenceManager.loadEvidences();
                    }
                    break;

                case 'verify':
                    if (window.verificationManager) {
                        verificationManager.clearResults();
                        verificationManager.loadUnverifiedEvidence();
                    }
                    break;

                case 'profile':
                    if (window.profileManager) {
                        profileManager.loadProfile();
                        await profileManager.loadUserStats();
                    }
                    break;

                case 'admin':
                    if (window.adminManager) {
                        // Initialize admin panel with real-time sync
                        window.adminManager.switchTab('users');
                        window.adminManager.startRealTimeSync();
                    }
                    break;

                case 'upload':
                    // Clear any previous upload form data
                    const uploadForm = document.getElementById('upload-form');
                    if (uploadForm) {
                        uploadForm.reset();
                        const previewDiv = document.getElementById('file-preview');
                        if (previewDiv) {
                            previewDiv.style.display = 'none';
                        }
                    }
                    break;
            }
        } catch (error) {
            console.error(`Error loading ${pageName} page data:`, error);
            showToast(`Error loading ${pageName} data`, 'error');
        }
    }

    logout() {
        if (window.authManager) {
            window.authManager.logout();
        } else {
            console.error('AuthManager not available');
            // Fallback: clear token and show login
            if (window.api) {
                window.api.setToken(null);
            }
            this.showPage('login');
        }

        // Clear browser history
        history.replaceState(null, '', '/');
    }

    // Handle application errors
    handleError(error, context = 'Application') {
        console.error(`${context} error:`, error);
        
        // Show user-friendly error message
        let message = 'An unexpected error occurred';
        
        if (error.message) {
            if (error.message.includes('network') || error.message.includes('fetch')) {
                message = 'Network error - please check your connection';
            } else if (error.message.includes('unauthorized') || error.message.includes('401')) {
                message = 'Session expired - please log in again';
                this.logout();
                return;
            } else {
                message = error.message;
            }
        }
        
        showToast(message, 'error');
    }

    // Get current page
    getCurrentPage() {
        return this.currentPage;
    }

    // Check if user can access a page
    canAccessPage(pageName) {
        // Check if authManager is available
        if (!window.authManager) {
            console.error('AuthManager not available for page access check');
            return pageName === 'login' || pageName === 'home' || pageName === 'about';
        }

        if (!window.authManager.isLoggedIn()) {
            return pageName === 'login' || pageName === 'home' || pageName === 'about';
        }

        switch (pageName) {
            case 'upload':
                return window.authManager.canUploadEvidence();
            case 'evidence':
            case 'verify':
                return window.authManager.canViewEvidence();
            case 'dashboard':
            case 'profile':
                return true;
            case 'admin':
                return window.authManager.canAccessAdminPanel();
            case 'home':
            case 'about':
                return true;
            default:
                return false;
        }
    }

    // Refresh current page
    async refresh() {
        await this.loadPageData(this.currentPage);
        showToast('Page refreshed', 'success');
    }

    // Cleanup function
    cleanup() {
        // Clear any intervals or timeouts
        if (window.adminManager) {
            window.adminManager.cleanup();
        }

        // Remove event listeners if needed
        // Save any pending data
    }
}

// Global error handler
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    if (window.app) {
        window.app.handleError(event.error, 'Global');
    }
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    if (window.app) {
        window.app.handleError(event.reason, 'Promise');
    }
    event.preventDefault();
});

// Initialize the application
const app = new App();

// Make app globally available
window.app = app;

// Service worker registration (if available)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
